.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-background {
  width: 100%;
  max-width: 400px;
  position: relative;
}

.login-content {
  position: relative;
  z-index: 1;
}

.login-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  color: #1890ff !important;
  margin-bottom: 8px !important;
  font-weight: 600;
  font-size: 28px;
}

.login-subtitle {
  font-size: 14px;
  color: #8c8c8c;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input-password {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input-password:hover {
  border-color: #40a9ff;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused,
.login-form .ant-input-password:focus,
.login-form .ant-input-password-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-button {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  transition: all 0.3s;
}

.login-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.login-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .login-title {
    font-size: 24px;
  }
}

/* 加载状态动画 */
.login-button.ant-btn-loading {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

/* 表单验证错误样式 */
.login-form .ant-form-item-has-error .ant-input-affix-wrapper,
.login-form .ant-form-item-has-error .ant-input-password {
  border-color: #ff4d4f;
}

.login-form .ant-form-item-has-error .ant-input-affix-wrapper:focus,
.login-form .ant-form-item-has-error .ant-input-password:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 图标样式 */
.login-form .anticon {
  color: #8c8c8c;
}

.login-form .ant-input-affix-wrapper:focus .anticon,
.login-form .ant-input-password:focus .anticon {
  color: #1890ff;
}
