/* 全局样式重置 */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* 确保body和html没有默认边距和填充 */
html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* Ant Design 全局样式覆盖 */
.ant-layout {
  background: #f0f2f5;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-btn {
  border-radius: 6px;
}

.ant-input, .ant-input-password {
  border-radius: 6px;
}
