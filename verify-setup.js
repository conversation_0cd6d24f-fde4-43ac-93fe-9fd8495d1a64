// 验证项目设置的简单脚本
const crypto = require('crypto');

console.log('🔍 验证 TMAI-TCamp 登录页面设置...\n');

// 1. 验证密码加密功能
console.log('1. 测试密码加密功能:');
const testPassword = 'test123456';
const encrypted = crypto.createHash('sha256').update(testPassword).digest('hex');
console.log(`   原始密码: ${testPassword}`);
console.log(`   SHA256加密: ${encrypted}`);
console.log(`   加密长度: ${encrypted.length} 字符`);
console.log(`   ✅ 密码加密功能正常\n`);

// 2. 检查关键文件是否存在
const fs = require('fs');
const path = require('path');

console.log('2. 检查关键文件:');
const criticalFiles = [
  'src/App.tsx',
  'src/pages/Login.tsx',
  'src/pages/Dashboard.tsx',
  'src/components/ProtectedRoute.tsx',
  'src/services/auth.ts',
  'src/services/api.ts',
  'src/utils/authStore.ts',
  'src/utils/crypto.ts',
  'package.json',
  'vite.config.ts'
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - 文件缺失`);
  }
});

// 3. 检查package.json依赖
console.log('\n3. 检查关键依赖:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const requiredDeps = [
    'antd',
    '@ant-design/icons',
    'react-router-dom',
    'axios',
    'crypto-js',
    'zustand',
    'react',
    'typescript'
  ];
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`   ✅ ${dep}: ${dependencies[dep]}`);
    } else {
      console.log(`   ❌ ${dep} - 依赖缺失`);
    }
  });
} catch (error) {
  console.log('   ❌ 无法读取 package.json');
}

// 4. 检查API配置
console.log('\n4. 检查API配置:');
try {
  const apiFile = fs.readFileSync('src/services/api.ts', 'utf8');
  if (apiFile.includes('127.0.0.1:8001')) {
    console.log('   ✅ API基础URL配置正确: http://127.0.0.1:8001/api/v1');
  } else {
    console.log('   ⚠️  API基础URL可能需要调整');
  }
} catch (error) {
  console.log('   ❌ 无法读取API配置文件');
}

console.log('\n🎉 验证完成！');
console.log('\n📋 下一步操作:');
console.log('1. 确保后端API服务运行在 http://127.0.0.1:8001');
console.log('2. 访问 http://localhost:5173 测试登录功能');
console.log('3. 使用有效的用户名和密码进行登录测试');
console.log('\n💡 提示: 密码会自动使用SHA256加密后发送到服务器');
